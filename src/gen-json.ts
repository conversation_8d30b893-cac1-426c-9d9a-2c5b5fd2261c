import type { ReleaseNote, InfoItem, API } from "../release-type";
import * as fs from "fs";

// 读取txt文件
const releaseTxt = fs.readFileSync("./src/text.txt", "utf-8");

// 平台映射表
const PLATFORM_MAP: { [key: string]: string } = {
  "Android": "android",
  "iOS": "ios",
  "Web": "web",
  "Windows": "windows",
  "macOS": "macos",
  "Flutter": "flutter",
  "Linux": "linux",
  "全平台": "all"
};

// 产品映射表
const PRODUCT_MAP: { [key: string]: string } = {
  "实时音视频": "video",
  "实时语音": "voice",
  "超低延迟直播": "live-stream",
  "互动视频（旧）": "rtc",
  "实时语音（旧）": "rtc",
  "即时通讯": "zim"
};

/**
 * 解析平台信息
 */
function parsePlatforms(platformText: string): string[] {
  const platforms: string[] = [];

  for (const [key, value] of Object.entries(PLATFORM_MAP)) {
    if (platformText.includes(key)) {
      if (value === "all") {
        return ["all"];
      }
      platforms.push(value);
    }
  }

  return platforms.length > 0 ? platforms : [];
}

/**
 * 解析产品信息
 */
function parseProducts(productText: string): string[] {
  const products: string[] = [];

  for (const [key, value] of Object.entries(PRODUCT_MAP)) {
    if (productText.includes(key)) {
      if (!products.includes(value)) {
        products.push(value);
      }
    }
  }

  return products;
}

/**
 * 解析API信息
 */
function parseAPIs(text: string): API[] {
  const apiRegex = /相关\s*API\s*[请项]?\s*参考[：:]\s*([^\n【]+)/gi;
  const apis: API[] = [];

  let match;
  while ((match = apiRegex.exec(text)) !== null) {
    if (match[1]) {
      const apiText = match[1].trim();
      // 分割API名称，支持多种分隔符
      const apiNames = apiText.split(/[、，,，\s]+/).filter(name => name.trim());

      for (const apiName of apiNames) {
        const cleanName = apiName.trim().replace(/[[\]]/g, ''); // 移除方括号
        if (cleanName && !apis.some(api => api.name === cleanName)) {
          apis.push({
            name: cleanName,
            link: `@${cleanName}`
          });
        }
      }
    }
  }

  return apis;
}

/**
 * 解析单个条目
 */
function parseItem(itemText: string): InfoItem {
  const lines = itemText.split('\n').map(line => line.trim()).filter(line => line);

  // 提取标题（第一行，去掉编号）
  const titleLine = lines[0] || '';
  const title = titleLine.replace(/^\d+\.\d*\s*/, '').trim();

  // 提取描述（除了标题、API、平台、产品之外的内容）
  const descriptionLines: string[] = [];
  let platformText = '';
  let productText = '';

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line) continue;

    if (line.startsWith('【平台】')) {
      platformText = line.replace('【平台】：', '').replace('【平台】:', '').trim();
    } else if (line.startsWith('【产品】')) {
      productText = line.replace('【产品】：', '').replace('【产品】:', '').trim();
    } else if (!line.includes('相关 API') && !line.includes('【注意】')) {
      descriptionLines.push(line);
    }
  }

  const description = descriptionLines.join('\n').trim();
  const apis = parseAPIs(itemText);
  const platforms = parsePlatforms(platformText);
  const products = parseProducts(productText);

  const item: InfoItem = {
    title,
    description,
    platform: platforms.length === 1 ? platforms[0] as any : platforms as any,
    product: products.length === 1 ? products[0] as any : products as any
  };

  if (apis.length > 0) {
    item.api = apis;
  }

  return item;
}

/**
 * 从分类章节中解析条目列表
 */
function parseItemsFromSection(sectionText: string): InfoItem[] {
  const items: InfoItem[] = [];

  // 按子条目分割（1.1, 1.2, 2.1, 2.2 等）
  const itemMatches = sectionText.split(/(?=\d+\.\d+\s)/);

  for (const itemText of itemMatches) {
    const trimmed = itemText.trim();
    if (!trimmed || !trimmed.match(/^\d+\.\d+/)) continue;

    const item = parseItem(trimmed);
    if (item.title) {
      items.push(item);
    }
  }

  return items;
}

/**
 * 解析整个文档
 */
function parseReleaseNotes(text: string): ReleaseNote {
  // 按主要分类分割文档
  const sections = text.split(/(?=\d+\.\s*[新改废问])/);

  const currentDate = new Date().toISOString().split('T')[0];
  const releaseNote: ReleaseNote = {
    verison: "1.0.0", // 默认版本，可以手动修改
    date: currentDate || "2025-01-01", // 当前日期
    content: {
      add: [],
      improve: [],
      delete: [],
      fix: []
    }
  };

  for (const section of sections) {
    const trimmedSection = section.trim();
    if (!trimmedSection) continue;

    if (trimmedSection.startsWith('1.') && trimmedSection.includes('新增功能')) {
      // 解析新增功能
      const items = parseItemsFromSection(trimmedSection);
      releaseNote.content.add = items;
    } else if (trimmedSection.startsWith('2.') && trimmedSection.includes('改进优化')) {
      // 解析改进优化
      const items = parseItemsFromSection(trimmedSection);
      releaseNote.content.improve = items;
    } else if (trimmedSection.startsWith('3.') && trimmedSection.includes('废弃删除')) {
      // 解析废弃删除
      const items = parseItemsFromSection(trimmedSection);
      releaseNote.content.delete = items;
    } else if (trimmedSection.startsWith('4.') && trimmedSection.includes('问题修复')) {
      // 解析问题修复
      const items = parseItemsFromSection(trimmedSection);
      releaseNote.content.fix = items;
    }
  }

  return releaseNote;
}

// 执行解析并生成JSON文件
const releaseNote = parseReleaseNotes(releaseTxt);

// 输出到控制台
console.log("解析完成，生成的JSON结构：");
console.log(JSON.stringify(releaseNote, null, 2));

// 保存到文件
fs.writeFileSync("./src/release-note-generated.json", JSON.stringify(releaseNote, null, 2), "utf-8");
console.log("已保存到 src/release-note-generated.json");
