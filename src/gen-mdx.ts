import type { InfoItem, ReleaseNote } from "../release-type";
import * as fs from "fs";
import * as path from "path";

const releaseJson = fs.readFileSync("./src/release-note-test.json", "utf-8");
const releaseNote: ReleaseNote = JSON.parse(releaseJson);
const { content } = releaseNote;

// 定义所有支持的平台和产品
const PLATFORMS = ["android", "ios", "web", "windows", "macos", "flutter", "linux"] as const;
const PRODUCTS = ["video", "voice", "live-stream", "rtc", "zim"] as const;

type Platform = typeof PLATFORMS[number];
type Product = typeof PRODUCTS[number];

/**
 * 过滤符合指定平台和产品的信息项
 */
function filterInfoItems(items: InfoItem[], targetPlatform: Platform, targetProduct: Product): InfoItem[] {
  return items.filter(item => {
    // 检查平台匹配
    const platformMatch = item.platform === "all" ||
                         (Array.isArray(item.platform) ? item.platform.includes(targetPlatform) : item.platform === targetPlatform);

    // 检查产品匹配
    const productMatch = Array.isArray(item.product) ? item.product.includes(targetProduct) : item.product === targetProduct;

    return platformMatch && productMatch;
  });
}

/**
 * 生成内容字符串
 */
function genContent(infoItems: InfoItem[]): string {
  if (!infoItems || infoItems.length === 0) {
    return "";
  }

  let index = 1;
  const content = infoItems.map((item) => {
    const apiLinks = item.api?.map((api) => ` [${api.name}](${api.link ? api.link : '@' + api.name})`).join("、") || "";
    const apiSection = apiLinks ? `\n\n相关 API 请参考：${apiLinks}` : "";
    return `${index++}. ${item.title}\n    ${item.description}${apiSection}`;
  }).join("\n\n");

  return content;
}

/**
 * 生成MDX模板
 */
function generateMdxTemplate(releaseNote: ReleaseNote, platform: Platform, product: Product): string {
  const filteredContent = {
    add: content.add ? filterInfoItems(content.add, platform, product) : [],
    improve: content.improve ? filterInfoItems(content.improve, platform, product) : [],
    delete: content.delete ? filterInfoItems(content.delete, platform, product) : [],
    fix: content.fix ? filterInfoItems(content.fix, platform, product) : []
  };

  const content_add = genContent(filteredContent.add);
  const content_improve = genContent(filteredContent.improve);
  const content_delete = genContent(filteredContent.delete);
  const content_fix = genContent(filteredContent.fix);

  // 构建MDX内容，只包含有内容的分类
  let mdxContent = `## ${releaseNote.verison} 版本

**发布日期：${releaseNote.date}**

`;

  // 只有当有内容时才添加对应的分类
  if (content_add) {
    mdxContent += `**新增功能**

${content_add}

`;
  }

  if (content_improve) {
    mdxContent += `**改进优化**

${content_improve}

`;
  }

  if (content_delete) {
    mdxContent += `**废弃删除**

${content_delete}

`;
  }

  if (content_fix) {
    mdxContent += `**问题修复**

${content_fix}

`;
  }

  mdxContent += `---`;

  return mdxContent;
}

/**
 * 确保目录存在
 */
function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 生成所有平台和产品的MDX文件
 */
function generateAllMdxFiles(): void {
  const baseOutputDir = "./result";

  for (const product of PRODUCTS) {
    for (const platform of PLATFORMS) {
      // 检查是否有该平台和产品的内容
      const hasContent = [
        ...(content.add || []),
        ...(content.improve || []),
        ...(content.delete || []),
        ...(content.fix || [])
      ].some(item => {
        const platformMatch = item.platform === "all" ||
                             (Array.isArray(item.platform) ? item.platform.includes(platform) : item.platform === platform);
        const productMatch = Array.isArray(item.product) ? item.product.includes(product) : item.product === product;
        return platformMatch && productMatch;
      });

      if (hasContent) {
        const outputDir = path.join(baseOutputDir, product, platform);
        const outputFile = path.join(outputDir, "release notes.mdx");

        ensureDirectoryExists(outputDir);

        const mdxContent = generateMdxTemplate(releaseNote, platform, product);
        fs.writeFileSync(outputFile, mdxContent, "utf-8");

        console.log(`Generated: ${outputFile}`);
      }
    }
  }
}

// 执行生成
generateAllMdxFiles();
