import type { InfoItem, ReleaseNote } from "../release-type";
import * as fs from "fs";
import * as path from "path";

const releaseJson = fs.readFileSync("./src/release-note-generated.json", "utf-8");
const releaseNote: ReleaseNote = JSON.parse(releaseJson);
const { content } = releaseNote;

// 定义所有支持的平台和产品
const PLATFORMS = ["android", "ios", "web", "windows", "macos", "flutter", "linux"] as const;
const PRODUCTS = ["video", "voice", "live-stream", "video-old", "voice-old", "zim"] as const;

type Platform = typeof PLATFORMS[number];
type Product = typeof PRODUCTS[number];

/**
 * 过滤符合指定平台和产品的信息项
 */
function filterInfoItems(items: InfoItem[], targetPlatform: Platform, targetProduct: Product): InfoItem[] {
  return items.filter(item => {
    // 检查平台匹配
    const platformMatch = item.platform === "all" ||
                         (Array.isArray(item.platform) ? item.platform.includes(targetPlatform) : item.platform === targetPlatform);

    // 检查产品匹配
    const productMatch = Array.isArray(item.product) ? item.product.includes(targetProduct) : item.product === targetProduct;

    return platformMatch && productMatch;
  });
}

/**
 * 转换【注意】为Warning组件
 */
function convertNotesToWarning(text: string): string {
  // 匹配【注意】：后面的内容
  const noteRegex = /【注意】：\s*([\s\S]*?)(?=\n\n|$)/g;

  return text.replace(noteRegex, (_match, noteContent: string) => {
    const trimmedContent = noteContent.trim();

    // 如果是简单的一行注意事项
    if (!trimmedContent.includes('\n') || !trimmedContent.match(/^\s*\d+\./m)) {
      return `<Warning title="注意">
${trimmedContent}
</Warning>`;
    }

    // 如果是多行列表格式的注意事项
    const lines = trimmedContent.split('\n').map((line: string) => line.trim()).filter((line: string) => line);
    const formattedLines = lines.map((line: string) => {
      // 处理编号列表项
      if (line.match(/^\d+\./)) {
        return `      ${line.replace(/；$/, '。')}`;
      }
      return `      ${line}`;
    });

    return `<Warning title="注意">
${formattedLines.join('\n')}
    </Warning>`;
  });
}

/**
 * 生成内容字符串
 */
function genContent(infoItems: InfoItem[]): string {
  if (!infoItems || infoItems.length === 0) {
    return "";
  }

  let index = 1;
  const content = infoItems.map((item) => {
    // 生成API链接，如果原始链接为空则使用@链接
    const apiLinks = item.api?.map((api) => {
      const link = api.link || `@${api.name}`;
      return ` [${api.name}](${link})`;
    }).join("、") || "";
    const apiSection = apiLinks ? `\n\n相关 API 请参考：${apiLinks}` : "";

    // 转换描述中的【注意】为Warning组件
    const processedDescription = convertNotesToWarning(item.description || "");

    return `${index++}. ${item.title}\n    ${processedDescription}${apiSection}`;
  }).join("\n\n");

  return content;
}

/**
 * 生成MDX模板
 */
function generateMdxTemplate(releaseNote: ReleaseNote, platform: Platform, product: Product): string {
  const filteredContent = {
    add: content.add ? filterInfoItems(content.add, platform, product) : [],
    improve: content.improve ? filterInfoItems(content.improve, platform, product) : [],
    delete: content.delete ? filterInfoItems(content.delete, platform, product) : [],
    fix: content.fix ? filterInfoItems(content.fix, platform, product) : []
  };

  const content_add = genContent(filteredContent.add);
  const content_improve = genContent(filteredContent.improve);
  const content_delete = genContent(filteredContent.delete);
  const content_fix = genContent(filteredContent.fix);

  // 构建MDX内容，只包含有内容的分类
  let mdxContent = `## ${releaseNote.verison} 版本

**发布日期：${releaseNote.date}**

`;

  // 只有当有内容时才添加对应的分类
  if (content_add) {
    mdxContent += `**新增功能**

${content_add}

`;
  }

  if (content_improve) {
    mdxContent += `**改进优化**

${content_improve}

`;
  }

  if (content_delete) {
    mdxContent += `**废弃删除**

${content_delete}

`;
  }

  if (content_fix) {
    mdxContent += `**问题修复**

${content_fix}

`;
  }

  mdxContent += `---`;

  return mdxContent;
}

/**
 * 确保目录存在
 */
function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 检查产品和平台组合是否支持
 */
function isSupportedPlatformForProduct(product: Product, platform: Platform): boolean {
  // video-old 只支持：android, ios, windows, linux, macos, web
  if (product === "video-old") {
    return ["android", "ios", "windows", "linux", "macos", "web"].includes(platform);
  }

  // voice-old 只支持：ios, android, windows
  if (product === "voice-old") {
    return ["ios", "android", "windows"].includes(platform);
  }

  // 其他产品支持所有平台
  return true;
}

/**
 * 生成所有平台和产品的MDX文件
 */
function generateAllMdxFiles(): void {
  const baseOutputDir = "./result";

  for (const product of PRODUCTS) {
    for (const platform of PLATFORMS) {
      // 检查产品和平台组合是否支持
      if (!isSupportedPlatformForProduct(product, platform)) {
        continue;
      }

      // 检查是否有该平台和产品的内容
      const hasContent = [
        ...(content.add || []),
        ...(content.improve || []),
        ...(content.delete || []),
        ...(content.fix || [])
      ].some(item => {
        const platformMatch = item.platform === "all" ||
                             (Array.isArray(item.platform) ? item.platform.includes(platform) : item.platform === platform);
        const productMatch = Array.isArray(item.product) ? item.product.includes(product) : item.product === product;
        return platformMatch && productMatch;
      });

      if (hasContent) {
        const outputDir = path.join(baseOutputDir, product, platform);
        const outputFile = path.join(outputDir, "release notes.mdx");

        ensureDirectoryExists(outputDir);

        const mdxContent = generateMdxTemplate(releaseNote, platform, product);
        fs.writeFileSync(outputFile, mdxContent, "utf-8");

        console.log(`Generated: ${outputFile}`);
      }
    }
  }
}

// 执行生成
generateAllMdxFiles();
