import type { InfoItem, ReleaseNote } from "../release-type";
import * as fs from "fs";

const releaseJson = fs.readFileSync("./src/release-note-test.json", "utf-8");

const releaseNote: ReleaseNote = JSON.parse(releaseJson);

const { content } = releaseNote;


function genContent(infoItems: InfoItem[]):string {
  let index = 1;
  const content = infoItems.map((item) =>{
    return `${index++}. ${item.title}\n    ${item.description}\n\n相关 API 请参考：${item.api?.map((api) => ` [${api.name}](${api.link?api.link:'@'+api.name})`).join("、")}`;
  })
  .join("\n");
  
  return content;
}

const content_add = content.add ? genContent(content.add) : null;
const content_improve = content.improve ? genContent(content.improve) : null;
const content_delete = content.delete ? genContent(content.delete) : null;
const content_fix = content.fix ? genContent(content.fix) : null;

const mdx_template_zh = `## ${releaseNote.verison} 版本

**发布日期：${releaseNote.date}**

**新增功能**

${content_add}

**改进优化**

${content_improve}

**废弃删除**

${content_delete}

**问题修复**

${content_fix}

---
`;

console.log(mdx_template_zh);
