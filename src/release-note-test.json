{"verison": "1.0.0", "date": "2025-01-01", "content": {"add": [{"title": "新增视频通话功能", "description": "支持高清视频通话，提供多种分辨率选择", "api": [{"name": "startVideoCall", "link": "https://docs.example.com/video/startVideoCall"}], "platform": ["android", "ios"], "product": ["video"]}, {"title": "新增语音降噪功能", "description": "AI智能降噪，提升通话质量", "api": [{"name": "enableNoiseSuppression", "link": "@enableNoiseSuppression"}], "platform": ["android", "ios", "web"], "product": ["voice", "video"]}, {"title": "新增Web端支持", "description": "支持在浏览器中进行音视频通话", "api": [{"name": "WebRTCEngine", "link": "@WebRTCEngine"}], "platform": ["web"], "product": ["video", "voice"]}, {"title": "新增直播推流功能", "description": "支持RTMP推流到第三方平台", "api": [{"name": "startLiveStreaming", "link": "@startLiveStreaming"}], "platform": ["android", "ios"], "product": ["live-stream"]}], "improve": [{"title": "优化Android端性能", "description": "减少CPU占用率30%，提升电池续航", "platform": ["android"], "product": ["video", "voice"]}, {"title": "改进iOS端兼容性", "description": "支持iOS 15及以上版本的新特性", "api": [{"name": "updateiOSCompatibility", "link": "@updateiOSCompatibility"}], "platform": ["ios"], "product": ["video", "voice", "live-stream"]}, {"title": "优化网络自适应算法", "description": "根据网络状况自动调整码率和分辨率", "platform": ["android", "ios", "web"], "product": ["video"]}], "delete": [{"title": "废弃旧版API", "description": "移除v0.9版本的废弃接口", "api": [{"name": "oldVideoAPI", "link": "@oldVideoAPI"}], "platform": ["android", "ios"], "product": ["video"]}], "fix": [{"title": "修复Android端崩溃问题", "description": "修复在特定机型上的内存泄漏导致的崩溃", "platform": ["android"], "product": ["video", "voice"]}, {"title": "修复Web端音频问题", "description": "修复Chrome浏览器下音频播放异常的问题", "platform": ["web"], "product": ["voice", "video"]}, {"title": "修复直播延迟问题", "description": "优化推流算法，降低直播延迟", "platform": ["android", "ios"], "product": ["live-stream"]}]}}