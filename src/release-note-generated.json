{"verison": "3.22.0", "date": "2025-08-25", "content": {"add": [{"title": "Android、iOS 支持主路设置麦克风音频源混入屏幕采集音频源", "description": "1. `ZegoAudioSourceMixConfig` 新增 `enableMixScreenCapture` 参数，用于在主路设置麦克风音频源时，支持混入屏幕共享音频数据一起推流。调用 [setAudioSource] 可以动态开启或关闭此功能。【Android、iOS、Flutter】\n2. `ZegoScreenCaptureConfig` 新增 `muteExtensMicrophone` 参数，用于屏蔽扩展进程的麦克风的声音。【iOS、Flutter】", "platform": ["android", "ios", "flutter"], "product": ["video", "voice", "live-stream"], "api": [{"name": "setAudioSource", "link": ""}, {"name": "startScreenCapture", "link": ""}, {"name": "updateScreenCaptureConfig", "link": ""}]}, {"title": "Android、iOS 屏幕共享提供启动回调和异常中断回调", "description": "启动屏幕共享时，可通过  [onMobileScreenCaptureStart]  监听屏幕共享是否启动成功和[onMobileScreenCaptureExceptionOccurred] 监听屏幕共享启动失败或中途断开异常。", "platform": ["android", "ios", "flutter"], "product": ["video", "voice", "live-stream"], "api": [{"name": "onMobileScreenCaptureStart", "link": ""}, {"name": "onMobileScreenCaptureExceptionOccurred", "link": ""}, {"name": "onScreenCaptureStart", "link": ""}, {"name": "onScreenCaptureExceptionOccurred", "link": ""}]}, {"title": "Android、iOS 屏幕共享支持设置画面固定横竖屏朝向", "description": "`ZegoScreenCaptureConfig` 新增了 `ZegoScreenCaptureOrientation` 参数，用于设置屏幕共享画面固定横竖屏朝向。", "platform": ["android", "ios", "flutter"], "product": ["video", "voice", "live-stream"], "api": [{"name": "startScreenCapture", "link": ""}, {"name": "updateScreenCaptureConfig", "link": ""}]}, {"title": "Android、iOS 屏幕共享支持音频包共享系统声音", "description": "需要开启此功能，请联系 ZEGO 技术支持。", "platform": ["android", "ios", "flutter"], "product": ["video", "voice", "live-stream"]}, {"title": "iOS 支持使用指定音量模式启动屏幕共享", "description": "`ZegoScreenCaptureConfig` 新增了 `ZegoScreenCaptureAudioDeviceMode` 参数，用于指定音频设备模式(通话音量或媒体音量)启动屏幕采集。\n【注意】：\n1. 只有当主路设置麦克风音频源时，音频设备模式才生效；\n2. 只作用于开启屏幕采集 [startScreenCapture] 接口。更新屏幕采集 [updateScreenCapture] 不生效；\n3. 采集过程中音频设备模式发生变化导致屏幕采集音频输出异常，可以通过 [onMobileScreenCaptureExceptionOccurred] 回调监听 AudioDeviceException，如有必要，需要重启采集；\n4. 停止采集后会恢复采集之前的音频设备模式。", "platform": ["ios", "flutter"], "product": ["video", "voice", "live-stream"], "api": [{"name": "startScreenCapture", "link": ""}, {"name": "updateScreenCaptureConfig", "link": ""}]}, {"title": "新增静态图片采集视频源", "description": "通过设置 [setVideoSource] 视频源为 `Picture`，即可推流一张指定的图片。", "platform": "all", "product": ["video", "live-stream"], "api": [{"name": "setVideoSource", "link": ""}]}, {"title": "半自动混流支持混流对齐能力", "description": "", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"], "api": [{"name": "ZegoAutoMixerTask", "link": ""}]}, {"title": "媒体播放器实例个数限制放开至 10个", "description": "", "platform": "all", "product": ["video", "voice", "live-stream"]}, {"title": "媒体播放器边下边播支持分片文件存储，避免一次性申请内存过大", "description": "【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "拉流切换功能支持强制切换模式", "description": "启用强制切换模式，可避免在弱网环境下，从高码率档位切换至低码率档位时长时间拉不到流的情况。", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"], "api": [{"name": "SwitchPlayingStream", "link": ""}]}, {"title": "新增 OpenGL 3 相关接口", "description": "新增 OpenGL 3 相关接口，在处理自定义视频时可以进行 OpenGL 3 的纹理绘制渲染。\n【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "android", "product": ["video", "voice", "live-stream"]}, {"title": "支持主动切到非系统默认网络", "description": "当无线网络质量较差时（未断开），可以主动切换到移动数据。\n【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": ["android", "ios"], "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "新增非主线程（ UI 线程）的音视频首帧耗时回调接口，可在主线程阻塞时更准确地统计首帧耗时", "description": "", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"], "api": [{"name": "onPlayerSyncRecvAudioFirstFrame", "link": ""}, {"name": "onPlayerSyncRecvVideoFirstFrame", "link": ""}, {"name": "onPlayerSyncRecvRenderVideoFirstFrame", "link": ""}]}, {"title": "支持接入微帧264编码器", "description": "【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "all", "product": ["video", "live-stream", "video-old"]}, {"title": "新增拉流链路上各环节数据指标的回调", "description": "【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "SDK 播放器支持 HLS 协议 拉流且支持分辨率自适应", "description": "【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"], "api": [{"name": "SwitchPlayingStream", "link": ""}]}, {"title": "拉取视频流时支持将视频流同时渲染到多个视图上", "description": "", "platform": ["android", "ios"], "product": ["video", "live-stream", "video-old"], "api": [{"name": "setPlayingCanvas", "link": ""}]}, {"title": "播放器拉混流可以将混流拆分成多个原始流渲染到多个画面", "description": "【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": ["android", "ios"], "product": ["video", "live-stream", "video-old"]}, {"title": "支持通过云端控制的方式下发云代理配置，并且动态生效", "description": "支持通过云端控制的方式下发云代理配置。当云端控制配置完成且SDK 拉取到最新的云端控制信息，新的云代理配置能够立即生效。\n【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "推拉流功能新增国密算法加密", "description": "【注意】：如需使用本功能，请联系 ZEGO 技术支持", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "混流 SEI 中的 uid 支持 string 类型", "description": "", "platform": ["android", "ios"], "product": ["video", "voice", "live-stream", "video-old", "voice-old"], "api": [{"name": "onPlayerRecvSEI", "link": ""}, {"name": "onPlayerSyncRecvSEI", "link": ""}]}, {"title": "支持第三方音频数据的混音功能", "description": "", "platform": ["windows", "macos", "linux"], "product": ["video", "voice", "live-stream", "video-old", "voice-old"], "api": [{"name": "enableAudioMixing", "link": ""}, {"name": "setAudioMixingVolume", "link": ""}, {"name": "muteLocalAudioMixing", "link": ""}]}, {"title": "支持 WMA 编码器", "description": "媒体播放器，音效播放器支持播放 wma 格式的音频文件", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "场景化配置新增  KTV 场景参数", "description": "", "platform": "flutter", "product": ["video", "voice", "live-stream"], "api": [{"name": "setRoomScenario", "link": ""}]}], "improve": [{"title": "SDK 内的 curl 库升级为 8.", "description": "", "platform": [], "product": []}, {"title": "版本", "description": "", "platform": "all", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}, {"title": "优化开启主体分割切换背景类型为视频时占用内存过大的问题", "description": "", "platform": "all", "product": ["video", "live-stream", "video-old"]}, {"title": "优化自然背景分割效果", "description": "", "platform": "all", "product": ["video", "live-stream", "video-old"]}, {"title": "优化 AI 超分锯齿", "description": "", "platform": "all", "product": ["video", "live-stream", "video-old"]}, {"title": "优化低照度增强效果，避免增强后出现画面发白的问题", "description": "", "platform": "all", "product": ["video", "live-stream", "video-old"]}, {"title": "优化 SDK 初始化到加入房间的耗时", "description": "", "platform": "android", "product": ["video", "voice", "live-stream", "video-old", "voice-old"]}], "delete": [], "fix": [{"title": "修复已知问题。", "description": "", "platform": "windows", "product": ["video", "voice", "live-stream"]}, {"title": "修复了同时异步调用 startPreview 和  startPublish 时重复采集摄像头导致结束推流时摄像头没有正常被释放的问题。", "description": "", "platform": "flutter", "product": ["video", "live-stream"]}, {"title": "修复 eventChannel 重复注册 zego_express_event_handler 数据在回调处理时出现错误提示的问题。", "description": "", "platform": "flutter", "product": ["video", "voice", "live-stream"]}, {"title": "修复远端设备状态回调报错没有触发 onRemoteCameraStateUpdate、onRemoteMicStateUpdate 事件。", "description": "", "platform": "flutter", "product": ["video", "voice", "live-stream"]}, {"title": "修复纯音频场景的摄像头权限弹框问题。", "description": "", "platform": "flutter", "product": ["video", "voice", "live-stream"]}, {"title": "修复 startPlayingStream 接口不传入视图容器参数无法拉流的问题。", "description": "", "platform": "flutter", "product": ["video", "voice", "live-stream"]}]}}