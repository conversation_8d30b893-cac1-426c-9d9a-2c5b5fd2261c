## 3.22.0 版本

**发布日期：2025-08-25**

**新增功能**

1. Android、iOS 支持主路设置麦克风音频源混入屏幕采集音频源
    1. `ZegoAudioSourceMixConfig` 新增 `enableMixScreenCapture` 参数，用于在主路设置麦克风音频源时，支持混入屏幕共享音频数据一起推流。调用 [setAudioSource] 可以动态开启或关闭此功能。【Android、iOS、Flutter】
2. `ZegoScreenCaptureConfig` 新增 `muteExtensMicrophone` 参数，用于屏蔽扩展进程的麦克风的声音。【iOS、Flutter】

相关 API 请参考： [setAudioSource](@setAudioSource)、 [startScreenCapture](@startScreenCapture)、 [updateScreenCaptureConfig](@updateScreenCaptureConfig)

2. Android、iOS 屏幕共享提供启动回调和异常中断回调
    启动屏幕共享时，可通过  [onMobileScreenCaptureStart]  监听屏幕共享是否启动成功和[onMobileScreenCaptureExceptionOccurred] 监听屏幕共享启动失败或中途断开异常。

相关 API 请参考： [onMobileScreenCaptureStart](@onMobileScreenCaptureStart)、 [onMobileScreenCaptureExceptionOccurred](@onMobileScreenCaptureExceptionOccurred)、 [onScreenCaptureStart](@onScreenCaptureStart)、 [onScreenCaptureExceptionOccurred](@onScreenCaptureExceptionOccurred)

3. Android、iOS 屏幕共享支持设置画面固定横竖屏朝向
    `ZegoScreenCaptureConfig` 新增了 `ZegoScreenCaptureOrientation` 参数，用于设置屏幕共享画面固定横竖屏朝向。

相关 API 请参考： [startScreenCapture](@startScreenCapture)、 [updateScreenCaptureConfig](@updateScreenCaptureConfig)

4. Android、iOS 屏幕共享支持音频包共享系统声音
    需要开启此功能，请联系 ZEGO 技术支持。

5. iOS 支持使用指定音量模式启动屏幕共享
    `ZegoScreenCaptureConfig` 新增了 `ZegoScreenCaptureAudioDeviceMode` 参数，用于指定音频设备模式(通话音量或媒体音量)启动屏幕采集。
1. 只有当主路设置麦克风音频源时，音频设备模式才生效；
2. 只作用于开启屏幕采集 [startScreenCapture] 接口。更新屏幕采集 [updateScreenCapture] 不生效；
3. 采集过程中音频设备模式发生变化导致屏幕采集音频输出异常，可以通过 [onMobileScreenCaptureExceptionOccurred] 回调监听 AudioDeviceException，如有必要，需要重启采集；
4. 停止采集后会恢复采集之前的音频设备模式。

相关 API 请参考： [startScreenCapture](@startScreenCapture)、 [updateScreenCaptureConfig](@updateScreenCaptureConfig)

6. 新增静态图片采集视频源
    通过设置 [setVideoSource] 视频源为 `Picture`，即可推流一张指定的图片。

相关 API 请参考： [setVideoSource](@setVideoSource)

7. 半自动混流支持混流对齐能力
    

相关 API 请参考： [ZegoAutoMixerTask](@ZegoAutoMixerTask)

8. 媒体播放器实例个数限制放开至 10个
    

9. 媒体播放器边下边播支持分片文件存储，避免一次性申请内存过大
    

10. 拉流切换功能支持强制切换模式
    启用强制切换模式，可避免在弱网环境下，从高码率档位切换至低码率档位时长时间拉不到流的情况。

相关 API 请参考： [SwitchPlayingStream](@SwitchPlayingStream)

11. 支持主动切到非系统默认网络
    当无线网络质量较差时（未断开），可以主动切换到移动数据。

12. 新增非主线程（ UI 线程）的音视频首帧耗时回调接口，可在主线程阻塞时更准确地统计首帧耗时
    

相关 API 请参考： [onPlayerSyncRecvAudioFirstFrame](@onPlayerSyncRecvAudioFirstFrame)、 [onPlayerSyncRecvVideoFirstFrame](@onPlayerSyncRecvVideoFirstFrame)、 [onPlayerSyncRecvRenderVideoFirstFrame](@onPlayerSyncRecvRenderVideoFirstFrame)

13. 支持接入微帧264编码器
    

14. 新增拉流链路上各环节数据指标的回调
    

15. SDK 播放器支持 HLS 协议 拉流且支持分辨率自适应
    

相关 API 请参考： [SwitchPlayingStream](@SwitchPlayingStream)

16. 拉取视频流时支持将视频流同时渲染到多个视图上
    

相关 API 请参考： [setPlayingCanvas](@setPlayingCanvas)

17. 播放器拉混流可以将混流拆分成多个原始流渲染到多个画面
    

18. 支持通过云端控制的方式下发云代理配置，并且动态生效
    支持通过云端控制的方式下发云代理配置。当云端控制配置完成且SDK 拉取到最新的云端控制信息，新的云代理配置能够立即生效。

19. 推拉流功能新增国密算法加密
    

20. 混流 SEI 中的 uid 支持 string 类型
    

相关 API 请参考： [onPlayerRecvSEI](@onPlayerRecvSEI)、 [onPlayerSyncRecvSEI](@onPlayerSyncRecvSEI)

21. 支持 WMA 编码器
    媒体播放器，音效播放器支持播放 wma 格式的音频文件

**改进优化**

1. 版本
    

2. 优化开启主体分割切换背景类型为视频时占用内存过大的问题
    

3. 优化自然背景分割效果
    

4. 优化 AI 超分锯齿
    

5. 优化低照度增强效果，避免增强后出现画面发白的问题
    

---