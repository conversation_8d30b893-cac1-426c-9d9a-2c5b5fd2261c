## 3.22.0 版本

**发布日期：2025-08-25**

**新增功能**

1. 新增静态图片采集视频源
    通过设置 [setVideoSource] 视频源为 `Picture`，即可推流一张指定的图片。

相关 API 请参考： [setVideoSource](@setVideoSource)

2. 半自动混流支持混流对齐能力
    

相关 API 请参考： [ZegoAutoMixerTask](@ZegoAutoMixerTask)

3. 媒体播放器实例个数限制放开至 10个
    

4. 媒体播放器边下边播支持分片文件存储，避免一次性申请内存过大
    

5. 拉流切换功能支持强制切换模式
    启用强制切换模式，可避免在弱网环境下，从高码率档位切换至低码率档位时长时间拉不到流的情况。

相关 API 请参考： [SwitchPlayingStream](@SwitchPlayingStream)

6. 新增非主线程（ UI 线程）的音视频首帧耗时回调接口，可在主线程阻塞时更准确地统计首帧耗时
    

相关 API 请参考： [onPlayerSyncRecvAudioFirstFrame](@onPlayerSyncRecvAudioFirstFrame)、 [onPlayerSyncRecvVideoFirstFrame](@onPlayerSyncRecvVideoFirstFrame)、 [onPlayerSyncRecvRenderVideoFirstFrame](@onPlayerSyncRecvRenderVideoFirstFrame)

7. 支持接入微帧264编码器
    

8. 新增拉流链路上各环节数据指标的回调
    

9. SDK 播放器支持 HLS 协议 拉流且支持分辨率自适应
    

相关 API 请参考： [SwitchPlayingStream](@SwitchPlayingStream)

10. 支持通过云端控制的方式下发云代理配置，并且动态生效
    支持通过云端控制的方式下发云代理配置。当云端控制配置完成且SDK 拉取到最新的云端控制信息，新的云代理配置能够立即生效。

11. 推拉流功能新增国密算法加密
    

12. 支持第三方音频数据的混音功能
    

相关 API 请参考： [enableAudioMixing](@enableAudioMixing)、 [setAudioMixingVolume](@setAudioMixingVolume)、 [muteLocalAudioMixing](@muteLocalAudioMixing)

13. 支持 WMA 编码器
    媒体播放器，音效播放器支持播放 wma 格式的音频文件

**改进优化**

1. 版本
    

2. 优化开启主体分割切换背景类型为视频时占用内存过大的问题
    

3. 优化自然背景分割效果
    

4. 优化 AI 超分锯齿
    

5. 优化低照度增强效果，避免增强后出现画面发白的问题
    

---