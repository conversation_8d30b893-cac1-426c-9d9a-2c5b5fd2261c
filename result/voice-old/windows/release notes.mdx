## 3.22.0 版本

**发布日期：2025-08-25**

**新增功能**

1. 半自动混流支持混流对齐能力
    

相关 API 请参考： [ZegoAutoMixerTask](@ZegoAutoMixerTask)

2. 媒体播放器边下边播支持分片文件存储，避免一次性申请内存过大
    <Warning title="注意">
如需使用本功能，请联系 ZEGO 技术支持
</Warning>

3. 拉流切换功能支持强制切换模式
    启用强制切换模式，可避免在弱网环境下，从高码率档位切换至低码率档位时长时间拉不到流的情况。

相关 API 请参考： [SwitchPlayingStream](@SwitchPlayingStream)

4. 新增非主线程（ UI 线程）的音视频首帧耗时回调接口，可在主线程阻塞时更准确地统计首帧耗时
    

相关 API 请参考： [onPlayerSyncRecvAudioFirstFrame](@onPlayerSyncRecvAudioFirstFrame)、 [onPlayerSyncRecvVideoFirstFrame](@onPlayerSyncRecvVideoFirstFrame)、 [onPlayerSyncRecvRenderVideoFirstFrame](@onPlayerSyncRecvRenderVideoFirstFrame)

5. 新增拉流链路上各环节数据指标的回调
    <Warning title="注意">
如需使用本功能，请联系 ZEGO 技术支持
</Warning>

6. SDK 播放器支持 HLS 协议 拉流且支持分辨率自适应
    <Warning title="注意">
如需使用本功能，请联系 ZEGO 技术支持
</Warning>

相关 API 请参考： [SwitchPlayingStream](@SwitchPlayingStream)

7. 支持通过云端控制的方式下发云代理配置，并且动态生效
    支持通过云端控制的方式下发云代理配置。当云端控制配置完成且SDK 拉取到最新的云端控制信息，新的云代理配置能够立即生效。
<Warning title="注意">
如需使用本功能，请联系 ZEGO 技术支持
</Warning>

8. 推拉流功能新增国密算法加密
    <Warning title="注意">
如需使用本功能，请联系 ZEGO 技术支持
</Warning>

9. 支持第三方音频数据的混音功能
    

相关 API 请参考： [enableAudioMixing](@enableAudioMixing)、 [setAudioMixingVolume](@setAudioMixingVolume)、 [muteLocalAudioMixing](@muteLocalAudioMixing)

10. 支持 WMA 编码器
    媒体播放器，音效播放器支持播放 wma 格式的音频文件

**改进优化**

1. 版本
    

---