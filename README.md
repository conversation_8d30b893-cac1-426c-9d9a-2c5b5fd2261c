# 发布日志生成器

---

每个大版本的发布日志都是多平台的。多个平台间存在一定的差异，将汇总的 changelog 结构化生成特定平台的发布日志。

具体格式参考：

- 实时音视频：[发布日志](https://doc-zh.zego.im/real-time-video-android-java/client-sdk/release-notes)
- 即时通讯：[发布日志](https://doc-zh.zego.im/zim-android/client-sdks/zim-release-notes)

## 结构化
发布日志公共结构大致如下：
```md
xxx 版本

发布日期： xxxx-xx-xx

新增功能：

改进优化：

废弃删除：

问题修复：

```

## 差异化处理

1. 区分产品： 有的产品有这条更新信息，有的没有
2. 区分平台： 有的平台有这条更新信息，有的没有


## 转换流程

飞书文档(txt) => 结构化数据(json) => 发布日志（mdx）