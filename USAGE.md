# 发布日志生成器使用说明

## 功能概述

这个工具可以将结构化的txt文件转换为JSON格式，然后根据平台和产品生成对应的MDX发布日志文件。

## 使用流程

### 1. 准备txt文件

将发布日志内容按以下格式整理到 `src/text.txt` 文件中：

**重要：文件首行必须包含版本号和日期信息**

```
2025/08/25_V3.22.0
1. 新增功能
1.1 功能标题1
功能描述内容
相关 API 请参考：api1、api2、api3
【平台】：Android、iOS、Flutter
【产品】：实时音视频、实时语音、超低延迟直播

1.2 功能标题2
功能描述内容
【平台】：全平台
【产品】：实时音视频

2. 改进优化
2.1 优化标题1
优化描述内容
【平台】：Android
【产品】：实时音视频、实时语音

3. 废弃删除
无

4. 问题修复
4.1 修复问题1
问题描述
【平台】：Flutter
【产品】：实时音视频
```

### 2. 生成JSON文件

运行以下命令将txt文件转换为JSON格式：

```bash
npm run gen-json
```

这会生成 `src/release-note-generated.json` 文件。

### 3. 生成MDX文件

运行以下命令根据JSON生成各平台和产品的MDX文件：

```bash
npm run gen-mdx
```

这会在 `result/` 目录下生成按产品和平台分类的MDX文件：

```
result/
├── video/
│   ├── android/release notes.mdx
│   ├── ios/release notes.mdx
│   ├── web/release notes.mdx
│   └── ...
├── voice/
│   ├── android/release notes.mdx
│   └── ...
└── live-stream/
    └── ...
```

## 支持的平台和产品

### 平台
- Android
- iOS
- Web
- Windows
- macOS
- Flutter
- Linux
- 全平台（映射为"all"）

### 产品
- 实时音视频（映射为"video"）
- 实时语音（映射为"voice"）
- 超低延迟直播（映射为"live-stream"）
- 互动视频（旧）（映射为"video-old"，支持：android, ios, windows, linux, macos, web）
- 实时语音（旧）（映射为"voice-old"，支持：android, ios, windows）
- 即时通讯（映射为"zim"）

## 特性

1. **智能解析**：自动识别标题、描述、API、平台和产品信息
2. **版本日期检查**：自动检查并解析版本号和日期，缺失时给出警告
3. **差异化生成**：每个平台和产品只包含相关的功能更新
4. **智能分类隐藏**：空的分类（如没有新增功能）不会显示
5. **API链接处理**：JSON中API链接留空，MDX生成时自动添加@链接
6. **Warning组件转换**：自动将【注意】转换为`<Warning>`组件格式
7. **平台限制支持**：旧产品支持特定平台（video-old: 6个平台，voice-old: 3个平台）
8. **灵活配置**：支持单平台/多平台、单产品/多产品配置

## 注意事项

1. **必须在文件首行提供版本和日期信息**，支持格式：
   - `2025/08/25_V3.22.0`
   - `版本：3.22.0` + `日期：2025-08-25`
   - `Version: 3.22.0` + `Date: 2025-08-25`
2. 确保txt文件格式正确，特别是【平台】和【产品】标记
3. API名称会自动去除方括号，JSON中链接留空，MDX生成时自动添加@链接
4. **【注意】内容会自动转换为Warning组件**：
   - 简单注意事项：`【注意】：内容` → `<Warning title="注意">内容</Warning>`
   - 列表注意事项：自动格式化为带缩进的列表格式
5. 如果某个平台或产品没有相关内容，不会生成对应的MDX文件
6. 旧产品有平台限制：video-old支持6个平台，voice-old支持3个平台
