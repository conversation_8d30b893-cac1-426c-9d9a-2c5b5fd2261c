export type MD = string;


export interface API {
  name: string;
  link: string;
}

export interface InfoItem {
  title: string;
  description ?: MD;
  api ?: API[];
  platform: ("android" | "ios" | "web" | "windows" | "macos" | "flutter" | "linux" | "all")[] | "android" | "ios" | "web" | "windows" | "macos" | "flutter" | "linux" | "all"; // all 表示全平台，包含：android、ios、windows、macos、flutter、linux(c++)
  product: ("video" | "voice" | "live-stream"| "video-old"| "voice-old"| "zim")[] | "video" | "voice" | "live-stream"| "video-old"| "voice-old"| "zim" ; // video-old 表示 互动视频（旧），voice-old 表示 实时语音（旧）
}


export interface ReleaseNote {
  verison: string;
  date: string;
  content: {
    add?: InfoItem[];
    improve?: InfoItem[];
    delete?: InfoItem[];
    fix?: InfoItem[];
  };
}
